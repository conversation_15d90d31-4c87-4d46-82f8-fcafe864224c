from imports import *

if TYPE_CHECKING:
    # This only runs for type checkers, not at runtime — safe to "reach inside"
    from endpoints.oauth_endpoint import OAuth2Verifier, OAuth2App

async def oauth_handle_commands(self: "OAuth2Verifier", app: "OAuth2App", tokens):
    ret_val = True
    if len(app.meltano_env) > 0:
        for command in app.commands:
            if command == "exit":
                if Globals.is_docker():
                    async def delayed_exit():
                        from asyncio import sleep
                        await sleep(15)
                        exit()
                    
                    from asyncio import create_task
                    create_task(delayed_exit())
                    ret_val = False
            elif command == "crawl":
                # Crawl the user's website in case it changed
                site_url = await self.get_token("website")
                if site_url != "":
                    print("Starting site-crawl")
                    from subprocess import run as subprocess_run
                    result = subprocess_run('cmd /c playwright install', shell=True, capture_output=True, text=True)
                    from inputs.crawler import Crawler
                    # Initialize the crawler
                    crawler = await <PERSON>rawler.setup_async()
                    sitemap = await self.get_token("website", "refresh_token")
                    if sitemap == "":
                        # Test with a single URL
                        await crawler.crawl(site_url, is_sitemap=False)
                    else:
                        # Test with a sitemap
                        sitemap_url = f"{site_url.rstrip('/')}/{sitemap}"
                        await crawler.crawl(sitemap_url, is_sitemap=True, max_concurrent=5)
                    # Clean up
                    await crawler.close_crawler()
                    print("Finished site-crawl")
            elif command == "debug":
                debug_modus = await self.get_token("debug")
                company_data = await self.get_token("debug", "refresh_token")
                Globals.set_debug("j" in debug_modus and "j" in company_data)
                # Set variable based on company_data==True
    return ret_val