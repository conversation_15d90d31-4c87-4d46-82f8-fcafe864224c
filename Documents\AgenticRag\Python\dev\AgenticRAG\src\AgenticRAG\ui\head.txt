<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verbind je tools - AskZaira</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #000000;
            color: #f8fafc;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        /* Animated background elements */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(99, 102, 241, 0.1) 0%, transparent 50%);
            animation: pulse 4s ease-in-out infinite alternate;
        }

        @keyframes pulse {
            0% { opacity: 0.3; }
            100% { opacity: 0.6; }
        }

        .container {
            width: 100%;
            max-width: 720px;
            margin: 0 auto;
            padding: 2rem;
            position: relative;
            z-index: 1;
        }

        .logo-section {
            text-align: center;
            margin-bottom: 3rem;
        }

        .logo {
            width: 48px;
            height: 48px;
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .brand-name {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .tagline {
            color: #94a3b8;
            font-size: 0.9rem;
        }

        .card {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 24px;
            padding: 2.5rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
            transition: all 0.3s ease;
        }

        .card:hover {
            border-color: rgba(59, 130, 246, 0.4);
            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.5);
        }

        .card-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .card-title {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .card-subtitle {
            color: #94a3b8;
            font-size: 1rem;
            line-height: 1.6;
        }

        .integrations-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 2rem;
            margin-bottom: 2.5rem;
        }

        .integration-item {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(71, 85, 105, 0.3);
            border-radius: 24px;
            padding: 1.5rem 1rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .integration-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 40%;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
            border-radius: 24px 24px 0 0;
        }

        .integration-item:hover {
            border-color: rgba(59, 130, 246, 0.4);
            transform: translateY(-4px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.4);
        }

        .integration-item.selected {
            border-color: rgba(59, 130, 246, 0.6);
            background: rgba(59, 130, 246, 0.1);
            box-shadow: 0 12px 40px rgba(59, 130, 246, 0.3);
        }

        .integration-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 1rem;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.2rem;
            color: white;
            position: relative;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
        }

        .integration-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 50%;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, transparent 100%);
            border-radius: 16px 16px 0 0;
        }

        .slack-icon { 
            background: linear-gradient(135deg, #4A154B 0%, #350d36 100%);
        }
        .calendar-icon { 
            background: linear-gradient(135deg, #4285f4 0%, #1a73e8 100%);
        }
        .gmail-icon { 
            background: linear-gradient(135deg, #ea4335 0%, #d33b2c 100%);
        }
        .drive-icon { 
            background: linear-gradient(135deg, #0f9d58 0%, #0d8043 100%);
        }
        .ads-icon { 
            background: linear-gradient(135deg, #4285f4 0%, #1565c0 100%);
        }
        .discord-icon { 
            background: linear-gradient(135deg, #5865f2 0%, #4752c4 100%);
        }
        .teams-icon {
            background: linear-gradient(135deg, #464EB8 0%, #6264A7 100%);
        }
        .dropbox-icon {
            background: linear-gradient(135deg, #0061FF 0%, #0052CC 100%);
        }
        .zoom-icon {
            background: linear-gradient(135deg, #2D8CFF 0%, #0B5CFF 100%);
        }
        .trello-icon {
            background: linear-gradient(135deg, #0079BF 0%, #026AA7 100%);
        }
        .pipeline-icon {
            background: linear-gradient(135deg, #28CA42 0%, #1FA832 100%);
        }
        .hubspot-icon {
            background: linear-gradient(135deg, #FF7A59 0%, #FF5C35 100%);
        }

        .file-upload-section {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(71, 85, 105, 0.3);
        }

        .upload-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-align: center;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .upload-subtitle {
            color: #94a3b8;
            font-size: 1rem;
            text-align: center;
            margin-bottom: 2rem;
        }

        .upload-area {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(20px);
            border: 2px dashed rgba(59, 130, 246, 0.3);
            border-radius: 24px;
            padding: 3rem 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .upload-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 40%;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.05) 0%, transparent 100%);
            border-radius: 24px 24px 0 0;
        }

        .upload-area:hover {
            border-color: rgba(59, 130, 246, 0.6);
            background: rgba(59, 130, 246, 0.05);
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
        }

        .upload-area.dragover {
            border-color: rgba(59, 130, 246, 0.8);
            background: rgba(59, 130, 246, 0.1);
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }

        .upload-text {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .primary-text {
            font-size: 1.1rem;
            font-weight: 600;
            color: #e2e8f0;
        }

        .secondary-text {
            font-size: 0.9rem;
            color: #94a3b8;
        }

        .supported-formats {
            font-size: 0.8rem;
            color: #64748b;
            background: rgba(71, 85, 105, 0.3);
            padding: 0.5rem 1rem;
            border-radius: 12px;
            display: inline-block;
        }

        .file-list {
            margin-top: 1.5rem;
            display: none;
        }

        .file-item {
            background: rgba(30, 41, 59, 0.6);
            border: 1px solid rgba(71, 85, 105, 0.3);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .file-icon {
            width: 32px;
            height: 32px;
            background: rgba(59, 130, 246, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
        }

        .file-details {
            display: flex;
            flex-direction: column;
        }

        .file-name {
            font-weight: 600;
            color: #e2e8f0;
            font-size: 0.9rem;
        }

        .file-size {
            font-size: 0.8rem;
            color: #94a3b8;
        }

        .remove-file {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #fca5a5;
            border-radius: 8px;
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .remove-file:hover {
            background: rgba(239, 68, 68, 0.3);
            border-color: rgba(239, 68, 68, 0.5);
        }

        .integration-name {
            font-size: 0.9rem;
            font-weight: 600;
            color: #e2e8f0;
        }

        .connect-button {
            width: 100%;
            background: linear-gradient(135deg, #3b82f6, #6366f1);
            border: none;
            border-radius: 16px;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
            position: relative;
            overflow: hidden;
        }

        .connect-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .connect-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(59, 130, 246, 0.4);
        }

        .connect-button:hover::before {
            left: 100%;
        }

        .connect-button:active {
            transform: translateY(0);
        }

        .security-note {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.2);
            border-radius: 12px;
            text-align: center;
            font-size: 0.85rem;
            color: #86efac;
        }

        .security-icon {
            margin-right: 0.5rem;
            font-size: 1rem;
        }

        @media (max-width: 640px) {
            .container {
                padding: 1rem;
            }
            
            .card {
                padding: 2rem 1.5rem;
            }
            
            .integrations-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }

        @media (max-width: 768px) and (min-width: 641px) {
            .integrations-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Loading animation */
        .loading {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .button-loading .loading {
            display: inline-block;
        }

        .button-loading {
            pointer-events: none;
            opacity: 0.8;
        }
    </style>
</head>