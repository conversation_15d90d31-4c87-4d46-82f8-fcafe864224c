from imports import *

from os import path as os_path
from typing import Optional
from json import dump as json_dump
from aiohttp import web, ClientSession
from aiohttp_oauth2_client.grant.authorization_code import AuthorizationCodeGrant
from urllib.parse import urlencode
from typing import TYPE_CHECKING
from datetime import datetime, timezone

from managers.manager_postgreSQL import PostgreSQLManager
from endpoints.oauth_commands import oauth_handle_commands

if TYPE_CHECKING:
    # This only runs for type checkers, not at runtime — safe to "reach inside"
    from aiohttp_oauth2_client.client import OAuth2Client

class OAuth2App:
    identifier: str = ""
    scopes: list[str] = []
    client_id: Optional[str] = None
    client_secret: Optional[str] = None
    auth_url: Optional[str] = None
    token_url: Optional[str] = None
    meltano_env: dict = {}
    commands: list[str] = []

    def create_input(self, identifier: str, input_fields: list[str]) -> "OAuth2App":
        """strings saved in order: access_token, refresh_token, token_type (50).
            Integers saved in order: expires_in, refresh_token_expires_in"""
        self.identifier = identifier
        self.scopes = input_fields
        return self

    def create_oauth(self, identifier: str, scopes: list[str], client_id: str, client_secret: str, auth_url: str, token_url: str) -> "OAuth2App":
        self.identifier = identifier
        self.scopes = scopes
        self.client_id = client_id
        self.client_secret = client_secret
        self.auth_url = auth_url
        self.token_url = token_url
        return self

    def set_meltano(self, meltano_env: dict[str, list[str]]) -> "OAuth2App":
        self.meltano_env = meltano_env
        return self

    def set_commands(self, commands: list[str]) -> "OAuth2App":
        self.commands = commands
        return self

class OAuth2Verifier:
    _instance = None
    _initialized = False

    apps: dict[str, OAuth2App] = {}

    bot_tokens: dict[str, dict[str, str]] = {}
    oauth_clients: dict[str, "OAuth2Client"] = {}

    def instantiate(self):
        self.apps["website"] = OAuth2App().create_input("website", ["Site URL", "Sitemap (Optional)"]) \
                                    .set_commands(["crawl"])
        self.apps["slack"] = OAuth2App().create_oauth("slack", ["app_mentions:read", "chat:write", "channels:read", "channels:history"], SLACK_BOT_CLIENT_ID, SLACK_BOT_CLIENT_SECRET, "https://slack.com/oauth/v2/authorize", "https://slack.com/api/oauth.v2.access")
        self.apps["gcalendar"] = OAuth2App().create_oauth("gcalendar", ["https://www.googleapis.com/auth/calendar.events.freebusy", "https://www.googleapis.com/auth/calendar.events"], GOOGLE_BOT_CLIENT_ID, GOOGLE_BOT_CLIENT_SECRET, "https://accounts.google.com/o/oauth2/v2/auth", "https://oauth2.googleapis.com/token")
        self.apps["gmail"] = OAuth2App().create_oauth("gmail", ["https://www.googleapis.com/auth/gmail.modify"], GOOGLE_BOT_CLIENT_ID, GOOGLE_BOT_CLIENT_SECRET, "https://accounts.google.com/o/oauth2/v2/auth", "https://oauth2.googleapis.com/token")
        self.apps["gdrive"] = OAuth2App().create_oauth("gdrive", ["https://www.googleapis.com/auth/drive","https://www.googleapis.com/auth/drive.readonly","https://www.googleapis.com/auth/admin.directory.group.readonly","https://www.googleapis.com/auth/admin.directory.group.member.readonly","https://www.googleapis.com/auth/admin.directory.user.readonly"], GOOGLE_BOT_CLIENT_ID, GOOGLE_BOT_CLIENT_SECRET, "https://accounts.google.com/o/oauth2/v2/auth", "https://oauth2.googleapis.com/token") \
                                    .set_meltano({"GDRIVE_ACCESS_TOKEN": "access_token", "GDRIVE_REFRESH_TOKEN": "refresh_token"})
        self.apps["googleads"] = OAuth2App().create_oauth("googleads", ["https://www.googleapis.com/auth/adwords"], GOOGLE_ADS_BOT_CLIENT_ID, GOOGLE_ADS_BOT_CLIENT_SECRET, "https://accounts.google.com/o/oauth2/v2/auth", "https://oauth2.googleapis.com/token") \
                                    .set_meltano({"TAP_GOOGLEADS_OAUTH_CREDENTIALS": "full_token", "TAP_GOOGLEADS_OAUTH_CREDENTIALS_REFRESH_TOKEN": "refresh_token"})
        self.apps["airtable"] = OAuth2App().create_input("airtable", "Access Token") \
                                    .set_meltano({"TAP_AIRTABLE_TOKEN": "access_token"})
        self.apps["discord"] = OAuth2App().create_oauth("discord", ["bot", "applications.commands"], DISCORD_BOT_CLIENT_ID, DISCORD_BOT_CLIENT_SECRET, "https://discord.com/oauth2/authorize?permissions=************", "https://discord.com/api/oauth2/token") \
                                    .set_commands(["exit"])
        self.apps["imap"] = OAuth2App().create_input("imap", ["Server Naam", "Netwerk port", "E-mail adres", "E-mail wachtwoord"])
        self.apps["smtp"] = OAuth2App().create_input("smtp", ["Server Naam", "Netwerk port", "E-mail adres", "E-mail wachtwoord"])
        self.apps["debug"] = OAuth2App().create_input("debug", ["Debug-Modus? (ja/nee)", "Deel bedrijfsdata? (ja/nee)"]) \
                                    .set_commands(["debug"])
        self.apps["woocommerce"] = OAuth2App().create_input("woocommerce", ["Consumer Key", "Consumer Secret", "Site URL", "Start Date (YYYY-MM-DD)"]) \
                                    .set_meltano({"TAP_WOOCOMMERCE_CONSUMER_KEY": "access_token",
                                                  "TAP_WOOCOMMERCE_CONSUMER_SECRET": "refresh_token",
                                                  "TAP_WOOCOMMERCE_SITE_URL": "token_type",
                                                  "TAP_WOOCOMMERCE_START_DATE": "expires_in"}) \
                                    .set_commands(["extract_woocommerce"])

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls) -> "OAuth2Verifier":
        return cls()

    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return

        try:
            instance.instantiate()
            from endpoints.api_endpoint import APIEndpoint
            from aiohttp_oauth2_client.client import OAuth2Client

            for identifier, app in instance.apps.items():
                if app.client_id and app.client_secret:
                    # Create an OAuth2 Client if the bot keys are provided. Without them we want a html page for user input
                    myscope = " ".join(app.scopes)

                    redirect_url = (f"http://localhost:8084" if Globals.get_debug() else "https://oauth.askzaira.com") + f"/oauth_redirect"
                    network_name = etc.helper_functions.get_value_from_env("ZAIRA_NETWORK_NAME", "python")
                    params = {
                        "client_id": app.client_id,
                        "redirect_uri": redirect_url,
                        "scope": myscope,
                        "state": f"{network_name}-{identifier}", # TODO: Needs improved
                        "response_type": "code",
                        "access_type": "offline",
                        "prompt": "consent",
                    }
                    grant = AuthorizationCodeGrant(
                        client_id=app.client_id,
                        client_secret=app.client_secret,
                        token_url=f"{app.token_url}",
                        authorization_url=app.auth_url + ("&" if "?" in app.auth_url else "?") + urlencode(params),
                        scope=myscope,
                    )
                    client = OAuth2Client(
                        grant=grant,
                    )
                    instance.oauth_clients[identifier] = client
                    APIEndpoint.get_instance().aio_app.add_routes([
                        web.get(f"/{identifier}/oauth", instance.oauth)
                    ])
                else:
                    APIEndpoint.get_instance().aio_app.add_routes([
                        web.get(f"/{identifier}/oauth", instance.oauth),
                        web.post(f"/{identifier}/oauth_redirect", instance.oauth_redirect_post),
                    ])
                APIEndpoint.get_instance().aio_app.add_routes([
                    web.get(f"/oauth_redirect", instance.oauth_redirect_get),
                ])

            instance._initialized = True

        except Exception as error:
            etc.helper_functions.exception_triggered(error)

    @classmethod
    async def get_token(cls, identifier: str, token_key: str = "access_token") -> str:
        instance = cls.get_instance()
        token = await instance.get_full_token(identifier)
        if token:
            if token_key == "full_token":
                return token
            return token[token_key]
        return ""

    @classmethod
    async def get_full_token(cls, identifier: str) -> Optional[dict[str]]:
        instance = cls.get_instance()

        if identifier in instance.bot_tokens:
            return instance.bot_tokens[identifier]

        await PostgreSQLManager.connect_to_database("vectordb")
        result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM oauth WHERE identifier = $1 ORDER BY created_at DESC LIMIT 1;", [identifier])
        if result:
            row = result[0]
            token = {
                "access_token": row["access_token"],
                "expires_in": row["expires_in"],
                "token_type": row["token_type"],
                "refresh_token": row["refresh_token"],
                "refresh_token_expires_in": row["refresh_token_expires_in"],
                "valid": row["valid"],
                "created_at": datetime.now(timezone.utc)
            }
            instance.bot_tokens[identifier] = token
            return instance.bot_tokens[identifier]

        return None

    @classmethod
    def get_client(cls, identifier: str) -> "OAuth2Client":
        return cls.get_instance().oauth_clients[identifier]

    async def save_tokens(self, identifier, tokens):
        ret_val = True
        tokens["valid"] = True
        self.bot_tokens[identifier] = tokens
        await PostgreSQLManager.connect_to_database("vectordb")
        query = """
        INSERT INTO oauth (
            identifier,
            access_token,
            expires_in,
            token_type,
            refresh_token,
            refresh_token_expires_in,
            valid,
            created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        ON CONFLICT (identifier)
        DO UPDATE SET
            access_token = EXCLUDED.access_token,
            expires_in = EXCLUDED.expires_in,
            token_type = EXCLUDED.token_type,
            refresh_token = EXCLUDED.refresh_token,
            refresh_token_expires_in = EXCLUDED.refresh_token_expires_in,
            valid = EXCLUDED.valid,
            created_at = EXCLUDED.created_at;
        """
        params = [
            identifier,
            tokens['access_token'],
            tokens['expires_in'],
            tokens['token_type'],
            tokens['refresh_token'],
            tokens['refresh_token_expires_in'],
            tokens['valid'],
            tokens['created_at'],
        ]
        await PostgreSQLManager.execute_query("vectordb", query, params)
        await PostgreSQLManager.close_connection("vectordb")
        # makedirs('./tokens', exist_ok=True)
        # with open(f'./tokens/{identifier}.json', "w") as f:
        #     json_dump(tokens, f)


        ret_val = ret_val & await self.save_to_env(identifier, tokens, "/meltano/project/.env")
        ret_val = ret_val & await self.save_to_env(identifier, tokens, "/app/.env")
        ret_val = ret_val & await oauth_handle_commands(self, self.apps[identifier], tokens)
        return ret_val

    async def save_to_env(self, identifier, tokens, env_path):
        ret_val = True
        # Add the token value to the meltano .env file
        if Globals.is_docker():
            # Read existing lines (if the file exists)
            lines = []
            if os_path.exists(env_path):
                with open(env_path, "r") as f:
                    lines = f.readlines()
            app = self.apps[identifier]
            if len(app.meltano_env) > 0:
                for key, value in app.meltano_env.items():
                    if value == "full_token":
                        new_token = tokens
                    elif identifier == "woocommerce" and key == "TAP_WOOCOMMERCE_START_DATE":
                        # Convert timestamp back to date format for WooCommerce
                        if tokens["expires_in"] > 0:
                            date_obj = datetime.fromtimestamp(tokens["expires_in"])
                            new_token = date_obj.strftime("%Y-%m-%d")
                        else:
                            new_token = "2020-01-01"  # Default start date
                    else:
                        new_token = tokens[value]
                    env_var_key = key

                    # Update or add the env variable
                    updated = False
                    for i, line in enumerate(lines):
                        if line.startswith(f"{env_var_key}="):
                            lines[i] = f"{env_var_key}={new_token}\n"
                            updated = True
                            break

                    if not updated:
                        lines.append(f"{env_var_key}={new_token}\n")

            # Write the updated lines back
            with open(env_path, "w") as f:
                f.writelines(lines)
        return ret_val

    async def invalidate_tokens(self, identifier):
        tokens = await self.get_full_token(identifier)
        tokens["valid"] = False
        with open(f'./tokens/{identifier}.json', "w") as f:
            json_dump(tokens, f)

    async def oauth(self, request: web.Request):
        identifier = request.rel_url.parts[1]
        client = self.oauth_clients.get(identifier)
        app = self.apps[identifier]
        if not client:
            html_content = f"""
            <html>
                <head><title>User Input</title></head>
                <body>
                    <h1>Enter Your Data</h1>
                    <form action="/{identifier}/oauth_redirect" method="post">"""
            for scope in app.scopes:
                        html_content += f"""<label for="{scope}">{scope.replace("_", " ")}:</label>
                                            <input type="{'text' if not 'wachtwoord' in scope.lower() else 'password'}" name="{scope}" {"" if ("Optional" in scope) else "required"}><br><br>"""

            html_content += """
                        <input type="submit" value="Submit">
                    </form>
                </body>
            </html>
            """
            return web.Response(text=html_content, content_type="text/html")

        authorization_url = client.grant.authorization_url
        #return web.Response(status=302,headers={'Location':str(authorization_url)})

        html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8" />
            <title>Redirecting...</title>
            <script>
                // Redirect immediately
                window.location.href = "{authorization_url}";
            </script>
        </head>
        <body>
            <p>Token aanvragen... Klik <a href='{authorization_url}'>hier</a> als dit niet automatisch laadt.</p>
        </body>
        </html>
        """

        return web.Response(text=html_content, content_type='text/html')

    async def oauth_redirect_post(self, request: web.Request):
        identifier = request.rel_url.parts[1]
        try:
            data = await request.post()
        except Exception:
            return web.json_response({"error": "Invalid Post"}, status=400)

        from datetime import datetime
        save_value = {
            "access_token": "",
            "expires_in": 0,
            "token_type": "",
            "refresh_token": "",
            "refresh_token_expires_in": 0,
            "valid": True,
            "created_at": datetime.now(timezone.utc).replace(tzinfo=None)
        }

        app = self.apps[identifier]

        # Handle WooCommerce specific field mapping
        if identifier == "woocommerce":
            for i, scope in enumerate(app.scopes):
                value = data.get(scope)
                if (not "Optional" in scope) and not value:
                    return web.json_response({"error": "Alle waardes dienen ingevoerd te worden."}, status=400)

                # Map WooCommerce fields to token structure
                if i == 0:  # Consumer Key
                    save_value["access_token"] = value
                elif i == 1:  # Consumer Secret
                    save_value["refresh_token"] = value
                elif i == 2:  # Site URL
                    save_value["token_type"] = value
                elif i == 3:  # Start Date
                    # Convert date to timestamp for expires_in field
                    try:
                        date_obj = datetime.strptime(value, "%Y-%m-%d")
                        save_value["expires_in"] = int(date_obj.timestamp())
                    except ValueError:
                        save_value["expires_in"] = 0
        else:
            # Original logic for other apps
            string_id = 0
            int_id = 0
            token_int_values = ["expires_in", "refresh_token_expires_in"]
            token_string_values = ["access_token", "refresh_token", "token_type"]
            from re import fullmatch
            for scope in app.scopes:
                value = data.get(scope)
                if (not "Optional" in scope) and not value:
                    return web.json_response({"error": "Alle waardes dienen ingevoerd te worden."}, status=400)

                if fullmatch(r"-?\d+", value):  # Matches optional minus sign and digits
                    save_value[token_int_values[int_id]] = int(value)
                    int_id += 1
                else:
                    save_value[token_string_values[string_id]] = value
                    string_id += 1

        if await self.save_tokens(identifier, save_value):
            return web.HTTPFound(location=f"{Globals.get_endpoint_address()}/complete_oauth_and_return")
        else:
            return web.HTTPFound(location=f"{Globals.get_endpoint_address()}/complete_oauth_and_restart")

    async def oauth_redirect_get(self, request: web.Request):
        if "state" in request.query:
            identifier = request.query.get("state").split("-", 1)[1]
        else:
            identifier = request.rel_url.parts[1]
        client = self.oauth_clients.get(identifier)
        app = self.apps[identifier]
        if not client:
            return web.Response(text="OAuth2 client not found for " + identifier, status=404)
        try:
            # Currently as first, probably should be inside the 'not code or not scope' if statement
            if request.can_read_body == True and request.text() != "":
                response_data = await request.json()
                token = response_data["token"]
                if not token:
                    return web.Response(text="Token not received", status=400)

                self.bot_tokens[identifier] = {"access_token":token}
                await self.save_tokens(identifier, self.bot_tokens[identifier])

                return web.HTTPFound(location=f"{Globals.get_endpoint_address()}/complete_oauth_and_return")
            else:
                code = request.query.get("code", "")
                scope = request.query.get("scope", "")
                permissions = request.query.get("permissions") # Currently Discord-specific. Code needs expanded if more than 1 OAuth fails to deliver the scope
                if not code or (not scope and not permissions):
                    return web.Response(text="Error: Geen code of scope meeverstuurd.")
                redirect_url = (f"http://localhost:8084" if Globals.get_debug() else f"https://oauth.askzaira.com") + f"/oauth_redirect"
                async with ClientSession() as session:
                    async with session.post(
                        app.token_url,
                        data={
                            "grant_type": "authorization_code",
                            "code": code,
                            "redirect_uri": redirect_url,
                            "client_id": app.client_id,
                            "client_secret": app.client_secret,
                        }
                    ) as resp:
                        html_content = f"""<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8" /><title>Er is iets fout gegaan...</title></head><body>
                                        <p>Authenticatie successvol binnengekomen maar scopes niet kunnen verifiëren. Klik <a href='{Globals.get_endpoint_address() + 'dashboard'}'>hier</a> om het opnieuw te proberen.</p>
                                        </body></html>"""
                        if resp.status != 200:
                            error_text = await resp.text()
                            print(f"Token request failed: {error_text}")
                            return web.Response(text=error_text, status=resp.status, content_type='application/json')
                        tokens = await resp.json()
                        # Check if all scopes have been granted
                        if (set(tokens["scope"].split()) == set(scope.split()) and set(tokens["scope"].split()) == self.apps[identifier].scopes) or permissions == "************":
                            guild_id = request.query.get("guild_id", "") # Specific to Discord
                            if guild_id != "":
                                tokens["token_type"] = guild_id
                                #await self.save_to_env("discord_guild_id", {"discord_guild_id": guild_id}, "/app/.env") # Only works from within Docker
                            save_value = {
                                "access_token": tokens["access_token"],
                                "expires_in": tokens["expires_in"],
                                "token_type": tokens["token_type"],
                                "refresh_token": tokens["refresh_token"],
                                "refresh_token_expires_in": tokens["refresh_token_expires_in"] if "refresh_token_expires_in" in tokens else 0,
                                "valid": True,
                                "created_at": datetime.now(timezone.utc).replace(tzinfo=None)
                            }
                            if await self.save_tokens(identifier, save_value):
                                return web.HTTPFound(location=f"{Globals.get_endpoint_address()}/complete_oauth_and_return")
                            else:
                                return web.HTTPFound(location=f"{Globals.get_endpoint_address()}/complete_oauth_and_restart")
                        else:
                            html_content = f"""<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8" /><title>Er is iets fout gegaan...</title></head><body>
                                            <p>Token aanvraag gefaald: Alle verzochte permissies moeten aangevinkt worden. Klik <a href='{Globals.get_endpoint_address() + 'dashboard'}'>hier</a> om het opnieuw te proberen.</p>
                                            </body></html>"""
                        return web.Response(text=html_content, content_type='text/html')

        except Exception as e:
            return web.Response(text=f"OAuth error: {str(e)}", status=500)
