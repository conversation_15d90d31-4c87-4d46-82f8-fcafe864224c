            </div>

            <div class="file-upload-section">
                <h3 class="upload-title">Of upload je bestanden</h3>
                <p class="upload-subtitle">Sleep bestanden hiernaartoe of klik om te selecteren</p>

                <div class="upload-area" onclick="document.getElementById('file-input').click()">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">
                        <span class="primary-text">Klik om bestanden te selecteren</span>
                        <span class="secondary-text">of sleep ze hiernaartoe</span>
                    </div>
                    <div class="supported-formats">PDF, DOC, XLS, CSV, TXT</div>
                </div>

                <input type="file" id="file-input" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.csv,.txt" style="display: none;">

                <div id="file-list" class="file-list"></div>
            </div>

            <button class="connect-button" onclick="processFiles()" id="process-button" style="display: none;">
                <span class="loading"></span>
                <span class="button-text">Bestanden Verwerken</span>
            </button>

            <div class="security-note">
                <span class="security-icon">🔒</span>
                Veilig verbonden via OAuth 2.0 - Jouw gegevens blijven privé
            </div>
        </div>
    </div>

    <script>
        let uploadedFiles = [];

        // Direct OAuth connection for integrations
        function connectService(url) {
            window.location.href = url;
        }

        // File upload handling
        const fileInput = document.getElementById('file-input');
        const uploadArea = document.querySelector('.upload-area');
        const fileList = document.getElementById('file-list');
        const processButton = document.getElementById('process-button');

        fileInput.addEventListener('change', function(event) {
			uploadedFiles = [...event.target.files];
			updateFileList();
			updateProcessButton();
		});

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            handleFiles({ target: { files: e.dataTransfer.files } });
        });

        function handleFiles(event) {
            const files = Array.from(event.target.files);

            files.forEach(file => {
                if (!uploadedFiles.find(f => f.name === file.name && f.size === file.size)) {
                    uploadedFiles.push(file);
                }
            });

            updateFileList();
            updateProcessButton();
        }

		// Function to update the file list display (from your existing code)
		function updateFileList() {
			const fileListDiv = document.getElementById('file-list');
			fileListDiv.innerHTML = ''; // Clear previous list

			if (uploadedFiles.length === 0) {
				fileListDiv.style.display = 'none';
				return;
			}

			fileListDiv.style.display = 'block';
			uploadedFiles.forEach((file, index) => {
				const fileItem = document.createElement('div');
				fileItem.classList.add('file-item');
				fileItem.innerHTML = `
					<span>${file.name} (${(file.size / 1024).toFixed(2)} KB)</span>
					<span class="remove-file" data-index="${index}">&#x2715;</span>
				`;
				fileListDiv.appendChild(fileItem);
			});

			// Add event listeners for remove buttons
			document.querySelectorAll('.remove-file').forEach(removeBtn => {
				removeBtn.addEventListener('click', (event) => {
					const indexToRemove = parseInt(event.target.dataset.index);
					uploadedFiles.splice(indexToRemove, 1);
					updateFileList();
					updateProcessButton();
				});
			});
		}

        function removeFile(index) {
            uploadedFiles.splice(index, 1);
            updateFileList();
            updateProcessButton();
        }

		// Function to update the visibility of the process button (from your existing code)
		function updateProcessButton() {
			const processButton = document.getElementById('process-button');
			if (uploadedFiles.length > 0) {
				processButton.style.display = 'block';
			} else {
				processButton.style.display = 'none';
			}
		}

        function getFileIcon(filename) {
            const ext = filename.split('.').pop().toLowerCase();
            const icons = {
                'pdf': 'PDF',
                'doc': 'DOC',
                'docx': 'DOC',
                'xls': 'XLS',
                'xlsx': 'XLS',
                'csv': 'CSV',
                'txt': 'TXT'
            };
            return icons[ext] || 'FILE';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

		// --- IMPORTANT: This is the updated processFiles function ---
		async function processFiles() {
			const processButton = document.getElementById('process-button');
			const buttonText = processButton.querySelector('.button-text');

			// Show loading state
			processButton.classList.add('button-loading');
			buttonText.textContent = 'Verwerken...';

			const formData = new FormData();
			uploadedFiles.forEach(file => {
				formData.append('files', file); // 'files' must match the name attribute in your old form input
			});

			try {
				// Create a timeout for the request (5 minutes)
				const controller = new AbortController();
				const timeoutId = setTimeout(() => controller.abort(), 300000); // 5 minutes

				const response = await fetch('/file_upload', {
					method: 'POST',
					body: formData,
					signal: controller.signal,
					// When using FormData, Content-Type header is automatically set by the browser
					// to multipart/form-data with the correct boundary. Do not set it manually.
				});

				clearTimeout(timeoutId);

				if (response.ok) {
					const result = await response.text(); // Assuming your backend returns text (e.g., "File '...' uploaded successfully.")
					console.log('Upload successful:', result);

					// Show success immediately
					buttonText.textContent = 'Voltooid!';
					processButton.classList.remove('button-loading');

					// Reset after success
					setTimeout(() => {
						uploadedFiles = [];
						updateFileList();
						updateProcessButton();
						// Optionally, display the success message from the backend in your UI
						alert(result); // For quick testing, you can use alert
					}, 2000);

				} else {
					console.error('Upload failed:', response.status, response.statusText);
					const errorText = await response.text();
					alert('Upload failed: ' + errorText); // Display error from backend
					// Reset button state on failure
					buttonText.textContent = 'Bestanden Verwerken';
					processButton.classList.remove('button-loading');
				}
			} catch (error) {
				console.error('Error during upload:', error);
				if (error.name === 'AbortError') {
					alert('Upload timed out. The file might be too large or the server is busy. Please try again.');
				} else {
					alert('An error occurred during upload: ' + error.message);
				}
				// Reset button state on error
				buttonText.textContent = 'Bestanden Verwerken';
				processButton.classList.remove('button-loading');
			}
		}
        // Interactive animations
        document.addEventListener('mousemove', (e) => {
            const cards = document.querySelectorAll('.integration-item');
            const mouseX = e.clientX;
            const mouseY = e.clientY;

            cards.forEach(card => {
                const rect = card.getBoundingClientRect();
                const cardX = rect.left + rect.width / 2;
                const cardY = rect.top + rect.height / 2;

                const angleX = (mouseY - cardY) / 30;
                const angleY = (cardX - mouseX) / 30;

                card.style.transform = `perspective(1000px) rotateX(${angleX}deg) rotateY(${angleY}deg)`;
            });
        });

        document.addEventListener('mouseleave', () => {
            document.querySelectorAll('.integration-item').forEach(card => {
                card.style.transform = '';
            });
        });
    </script>
</body>