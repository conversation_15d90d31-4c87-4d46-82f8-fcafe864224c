from imports import *

import logging
from os import getcwd
from os import path as os_path
from os import makedirs as os_makedirs
from asyncio import sleep
from ssl import create_default_context, Purpose
from aiohttp import web
from agno.agent import Agent, RunResponse
from agno.models.ollama import Ollama
from agno.knowledge.pdf_url import PDFUrlKnowledgeBase
from agno.vectordb.qdrant import Qdrant
from agno.storage.agent.postgres import PostgresAgentStorage
from agno.embedder.fastembed import FastEmbedEmbedder
from botbuilder.core.integration import aiohttp_error_middleware

from etc.helper_functions import *
from managers.manager_retrieval import RetrievalManager
from managers.manager_meltano import MeltanoManager
from endpoints.oauth_endpoint import OAuth2Verifier
from managers.manager_logfire import LogFire
from managers.manager_users import ZairaUserManager
from endpoints.mybot_generic import MyBot_Generic

@web.middleware
async def logfire_middleware(request, handler):
    response = await LogFire.logfire_middleware(request, handler)
    return response

@web.middleware
async def ip_check_middleware(request, handler):
    request['real_ip'] = (
        request.headers.get('CF-Connecting-IP') or
        request.headers.get('X-Forwarded-For', request.remote)
    ).split(',')[0].strip()
    response = await handler(request)
    return response

class APIEndpoint:
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    _instance = None
    aio_app: web.Application = None
    bot_generic: MyBot_Generic = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(APIEndpoint, cls).__new__(cls, *args, **kwargs)
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.initialized = True
            self.agent = None

    @classmethod
    def get_instance(cls):
        return cls._instance or cls()

    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        instance.aio_app = web.Application(middlewares=[aiohttp_error_middleware, logfire_middleware, ip_check_middleware])

        instance.aio_app.add_routes([
            web.get('/', instance.home),
            web.get('/dashboard', instance.dashboard),
            web.get('/complete_oauth_and_return', instance.complete_oauth_and_return),
            web.post('/slack/events', instance.slack_events),
            #web.get('/gmail_test', instance.GMAIL),
            web.get('/ask', instance.ask),
            web.get('/ask_url', instance.ask_url),
            web.get('/delayed/ask', instance.ask_delayed),
            web.get('/delayed/status/{task_id}', instance.check_status),
            web.get('/askAgno', instance.ask_agno),
            web.get('/managers/meltano/ConvertSQLToVectorStore', instance.convert_sql_to_vectorstore),
            web.post('/v1/embeddings', instance.embedding_openai),
            web.post('/onnx/v1/embeddings', instance.embedding_onnx),
            web.post('/file_upload', instance.handle_file_upload)
        ])
        instance.bot_generic = MyBot_Generic(instance, "HTTP")

    async def start_app(self, app, host, port,ssl_context=None):
        runner = web.AppRunner(app)
        await runner.setup()
        site = web.TCPSite(runner, host, port,ssl_context=ssl_context)
        await site.start()
        print(f"Server started at http://{host}:{port}")

    @classmethod
    async def late_setup(cls):
        if Globals.get_debug() == False and Globals.is_docker == False:
            ssl_context = create_default_context(Purpose.CLIENT_AUTH)
            if Globals.is_docker(): # -.- always false
                ssl_cert = '/ssl/fullchain.pem'
                ssl_key = '/ssl/privkey.pem'
                ssl_context.load_cert_chain(ssl_cert, ssl_key)
            else:
                current_directory = getcwd()
                ssl_cert = f'{current_directory}/ssl/fullchain.pem'
                ssl_key = f'{current_directory}/ssl/privkey.pem'
                ssl_context.load_cert_chain(ssl_cert, ssl_key)
            await cls.get_instance().start_app(cls.get_instance().aio_app, host=IP_PYTHON, port=ZAIRA_PYTHON_PORT, ssl_context=ssl_context)
            #await web.run_app(cls.get_instance().aio_app, host=IP_PYTHON, port=PORT_PYTHON, ssl_context=ssl_context)
        else:
            await cls.get_instance().start_app(cls.get_instance().aio_app, host="0.0.0.0", port=ZAIRA_PYTHON_PORT)
            #Thread(target=lambda: web.run_app(cls.get_instance().aio_app, host=["localhost", "0.0.0.0"], port=PORT_PYTHON),daemon=True).start()
        routes = cls.get_instance().aio_app.router.routes()
        port = get_value_from_env("ZAIRA_PYTHON_PORT", f"{ZAIRA_PYTHON_PORT}")
        print(f"{int(port)} endpoint routes:")
        for route in routes:
            print(f"{Globals.get_endpoint_address()}{str(route.resource.canonical)}")

    async def home(self, request: web.Request) -> web.Response:
        print(f"Endpoint home called from IP: {request['real_ip']}")
        self.logger.info("Homepage accessed")
        return web.Response(text="<html><head><title>Home</title></head><body>Home</body></html>", content_type='text/html')

    # You can also make this dynamic or template-rendered
    async def load_content_dashboard(self, request: web.Request) -> str:
        ret_val = ""
        for identifier in OAuth2Verifier.get_instance().apps.keys():
            token = await OAuth2Verifier.get_token(identifier)
            url = f"{Globals.get_endpoint_address()}/{identifier}/oauth"
            site = f"""<div class="integration-item" onclick="connectService('{url}')"><div class="integration-icon {"teams" if not token else "pipeline"}-icon">{identifier[0:2].upper() if not token else "OK"}</div>"""
            #if token == "":
            #    site = site + f"""<a href='{url}'>{url}</a><br />""" #  target='_blank'
                #if len(scopes) > 0:
                #    site = site + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;["
                #for scope in scopes:
                #    site = site + f"{scope}, "
                #site = site[:-2] + "]<br />"
            #else:
            #    site = site + f"<a href='{url}'>{url}</a><br />" #  target='_blank'
            #    if len(scopes) > 0:
            #        site = site + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;OK"
            #    site = site + "<br />"
            ret_val += site + f"""<div class="integration-name">{identifier}</div></div>"""
        return ret_val
        ret_val += """<br /><br /><h1>Upload a File</h1><script>
            function addFileInput() {
                const container = document.getElementById('fileInputs');
                const input = document.createElement('input');
                input.type = 'file';
                input.name = 'file'; // Same name allows server to receive a list
                container.appendChild(document.createElement('br'));
                container.appendChild(input);
            }
        </script>"""
        if 'uploaded' in request.query:
            ret_val += f"File '{request.query.get('uploaded')}' uploaded successfully."
        ret_val += "<br />"
        ret_val += """<form action="/file_upload" method="post" enctype="multipart/form-data" id="uploadForm">
            <div id="fileInputs"><input type="file" name="files" multiple required></div>
            <br /><button type="button" onclick="addFileInput()">Add More Files</button><br /><br />"""

        ret_val += """<button type='submit'>Upload</button>
        </form>"""
        ret_val += """<div class="file-upload-section">
                <h3 class="upload-title">Of upload je bestanden</h3>
                <p class="upload-subtitle">Sleep bestanden hiernaartoe of klik om te selecteren</p>

                <div class="upload-area" onclick="document.getElementById('file-input').click()">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">
                        <span class="primary-text">Klik om bestanden te selecteren</span>
                        <span class="secondary-text">of sleep ze hiernaartoe</span>
                    </div>
                    <div class="supported-formats">PDF, DOC, XLS, CSV, TXT</div>
                </div>

                <input type="file" id="file-input" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.csv,.txt" style="display: none;">

                <div id="file-list" class="file-list"></div>
            </div>"""
        return ret_val

    async def dashboard(self, request: web.Request) -> web.Response:
        if Globals.is_docker():
            ui_folder = "/app/ui/"
        else:
            ui_folder = getcwd() + "/src/AgenticRAG/ui/"
        with open(os_path.join(ui_folder, "head.txt"), "r", encoding="utf-8") as f:
            HEAD_HTML = f.read()

        with open(os_path.join(ui_folder, "header.txt"), "r", encoding="utf-8") as f:
            HEADER_HTML = f.read()

        with open(os_path.join(ui_folder, "footer.txt"), "r", encoding="utf-8") as f:
            FOOTER_HTML = f.read()

        print(f"Endpoint dashboard called from IP: {request['real_ip']}")
        content = await self.load_content_dashboard(request)
        site = f"""<!DOCTYPE html>
                   <html lang="nl">
                   {HEAD_HTML}
                   {HEADER_HTML}
                   {content}
                   {FOOTER_HTML}
                   </html>"""

        return web.Response(text=site, content_type='text/html')

    async def complete_oauth_and_return(self, request: web.Request):
        target_url = f"{Globals.get_endpoint_address()}/dashboard"
        html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8" />
            <title>Redirecting...</title>
            <script>
                // Redirect after 1.5 seconds
                setTimeout(function() {{
                    window.location.href = "{target_url}";
                }}, 1500);
            </script>
        </head>
        <body>
            <p>Token succesvol opgeslagen! Klik <a href='{Globals.get_endpoint_address()}/dashboard'>hier</a> om terug te gaan naar de OAuth koppeling pagina als dit niet automatisch gebeurt.</p>
        </body>
        </html>
        """

        return web.Response(text=html_content, content_type='text/html')

    async def complete_oauth_and_restart(self, request: web.Request):
        target_url = f"{Globals.get_endpoint_address()}/dashboard"
        html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8" />
            <title>Redirecting...</title>
            <script>
                // Redirect after 60 seconds
                setTimeout(function() {{
                    window.location.href = "{target_url}";
                }}, 60000);
            </script>
        </head>
        <body>
            <p>Token succesvol opgeslagen, server wordt herstart! Klik <a href='{Globals.get_endpoint_address()}/dashboard'>hier</a> om terug te gaan naar de OAuth koppeling pagina als dit niet automatisch gebeurt.</p>
        </body>
        </html>
        """

        return web.Response(text=html_content, content_type='text/html')

    # Route to handle file upload
    async def handle_file_upload(self, request: web.Request):
        reader = await request.multipart()
        field = await reader.next()

        if field.name != 'files':
            return web.Response(text='Expected a "files" field', status=400)

        # Get the uploaded files from a single multi-part field
        filenames = field.filename
        if not filenames:
            return web.Response(text='No files uploaded.', status=400)

        if Globals.is_docker():
            path = '/meltano/output'
        else:
            from os import getcwd
            path = getcwd() + '/src/meltano/output'
        os_makedirs(path, exist_ok=True)

        files_uploaded = []

        # Handle multiple files in the same "files" field
        while field:
            filename = field.filename
            if filename:
                save_path = os_path.join(path, filename)
                with open(save_path, 'wb') as f:
                    while True:
                        chunk = await field.read_chunk()
                        if not chunk:
                            break
                        f.write(chunk)
                files_uploaded.append(filename)

            field = await reader.next()
            if not field or field.name != 'files':
                break

        if files_uploaded:
            try:
                print(f"Starting file processing for {len(files_uploaded)} files...")
                await MeltanoManager.ConvertFilesToVectorStore(path, None)
                uploaded_names = ','.join(files_uploaded)
                print(f"Successfully processed files: {uploaded_names}")
                return web.Response(text=f" Bestanden zijn succesvol verwerkt! {len(files_uploaded)} bestand(en) zijn opgeslagen in de kennisbank.")#HTTPFound(location=f"{Globals.get_endpoint_address()}/dashboard?uploaded={uploaded_names}")
            except Exception as e:
                print(f"Error processing files: {e}")
                import traceback
                traceback.print_exc()
                return web.Response(text=f"Error processing files: {str(e)}", status=500)
        else:
            return web.Response(text='No valid files found.', status=400)


    async def check_status(self, request: web.Request) -> web.Response:
        user_guid = request.match_info["guid"]
        if user_guid != "":
            user = await ZairaUserManager.find_user(user_guid)
            if user.my_task != None:
                return web.json_response({"status": self.get_task_status()["status"]})
            else:
                return web.json_response({"status": "not running"})
        return web.json_response({"error": {"message": "check_status"}}, status=400)

    # async def GMAIL(self, request: web.Request) -> web.Response:
    #     query = request.query.get("query", "")
    #     print(f"Endpoint GMAIL called with: {query} from IP: {request['real_ip']}")
    #     token = OAuth2Verifier.get_token("google")
    #     if token != "":
    #         from googleapiclient.discovery import build
    #         from google.oauth2.credentials import Credentials
    #         # Call the Gmail API
    #         token_info = {
    #             "client_id": OAuth2Verifier.get_instance().oauth_client_keys["google"][0],
    #             "client_secret": OAuth2Verifier.get_instance().oauth_client_keys["google"][1],
    #             "refresh_token": OAuth2Verifier.get_instance().get_token(identifier="google", token_type="refresh_token"),
    #             "token_uri": OAuth2Verifier.get_instance().oauth_auth_token_urls["google"][1],
    #             "access_token": OAuth2Verifier.get_instance().get_token(identifier="google"),
    #             "expires_in": OAuth2Verifier.get_instance().get_token(identifier="google", token_type="expires_in"),
    #             "scopes": OAuth2Verifier.get_instance().apps["google"].scopes,
    #         }
    #         service = build("gmail", "v1", credentials=Credentials.from_authorized_user_info(token_info))
    #         users = service.users()
    #         profile = users.getProfile(userId="me").execute()
    #         results = users.labels().list(userId="me").execute()
    #         labels = results.get("labels", [])

    #         if not labels:
    #             print("No labels found.")
    #             return
    #         print("Labels:")
    #         for label in labels:
    #             print(label["name"])
    #         return web.json_response({"user profile": profile, "labels": labels})
    #     return web.Response("GMAIL request failed. Most likely no OAuth set up yet. Link should've opened.")

    async def slack_events(self, request: web.Request) -> web.Response:
        data = await request.json()
        if data.get('type') == 'url_verification':
            return web.json_response({'challenge': data['challenge']})

        self.logger.info(f"Received Slack event: {data}")
        return web.json_response({"status": "ok"})

    async def ask(self, request: web.Request) -> web.Response:
        query = request.query.get("query", "")
        print(f"Endpoint ask called with: {query} from IP: {request['real_ip']}")
        query_engine = Globals.get_query_engine_default()
        result = query_engine.query(query)
        return web.json_response({"response": str(result)})

    async def ask_url(self, request: web.Request) -> web.Response:
        query = request.query.get("query", "")
        print(f"Endpoint ask_url called with: {query} from IP: {request['real_ip']}")
        query_engine = Globals.get_query_engine_default()
        result = query_engine.query(query)
        return web.Response(text=f"<html><head><title>Query Result</title></head><body>{result}</body></html>", content_type='text/html')

    async def ask_delayed(self, request) -> web.Response:
        query = request.query.get("query", "")
        user_guid = request.query.get("guid", "")
        if query != "" and user_guid != "":
            print(f"Endpoint ask_delayed called with: {query} from IP: {request['real_ip']}")
            user = await ZairaUserManager.find_user(user_guid)
            await user.on_message(query, calling_bot=self.bot_generic, attachments=[], original_message=None)
            return web.json_response({'message': 'Task started!', 'task_id': user.my_task.task_id}, status=202)
        return web.json_response({"error": {"message": "ask_delayed"}}, status=400)

    async def ask_agno(self, request: web.Request) -> web.Response:
        query = request.query.get("query", "")
        print(f"Endpoint ask_agno called with: {query} from IP: {request['real_ip']}")
        qdrant_url = f"http://localhost:{PORT_QDRANT}"
        collection_name = "thai-recipe-index"
        embedder = FastEmbedEmbedder(dimensions=EMBEDDING_SIZE)

        vector_db = Qdrant(
            collection=collection_name,
            url=qdrant_url,
            embedder=embedder,
        )
        host = "pgvector:5432" if Globals.is_docker() else f"pgvector:{PORT_POSTGRESQL}"
        db_url = f"postgresql+psycopg://ai:ai@{host}/ai"

        knowledge_base = PDFUrlKnowledgeBase(
            urls=["https://agno-public.s3.amazonaws.com/recipes/ThaiRecipes.pdf"],
            vector_db=vector_db,
        )
        knowledge_base.load(recreate=True)

        if not self.agent:
            self.agent = Agent(
                model=Ollama(id=AGENT_MODEL_OLLAMA),
                description="You are a Thai cuisine expert!",
                knowledge=knowledge_base,
                storage=PostgresAgentStorage(table_name="agent_sessions", db_url=db_url),
                markdown=True,
            )

        response: RunResponse = self.agent.run(query)
        if isinstance(response, etc.helper_functions.get_any_message_as_type()):
            response = response.content
        return web.json_response({"response": response})

    async def embedding_openai(self, request: web.Request) -> web.Response:
        try:
            data = await request.json()
            input_texts = data.get("input")
            if not isinstance(input_texts, (str, list)):
                raise ValueError("'input' must be a string or list of strings")
            model = data.get("model", "default-model")

            embeddings = RetrievalManager.get_embeddings_dense(input_texts)

            response_data = {
                "object": "list",
                "data": [{"object": "embedding", "embedding": embedding, "index": idx} for idx, embedding in enumerate(embeddings)],
                "model": model,
                "usage": {"prompt_tokens": 0, "total_tokens": 0}
            }

            return web.json_response(response_data)

        except ValueError as ve:
            return web.json_response({"error": {"message": str(ve)}}, status=400)
        except Exception as e:
            self.logger.exception("Embedding generation failed")
            return web.json_response({"error": {"message": f"Internal server error: {str(e)}"}}, status=500)

    async def embedding_onnx(self, request: web.Request) -> web.Response:
        try:
            data = await request.json()
            input_texts = data['input']
            embeddings = self.get_embeddings(input_texts)

            response_data = {
                "data": [{"embedding": embedding.tolist(), "index": idx} for idx, embedding in enumerate(embeddings)]
            }
            return web.json_response(response_data)

        except KeyError:
            return web.json_response({"error": "Missing 'input' field"}, status=400)

    async def convert_sql_to_vectorstore(self, request: web.Request) -> web.Response:
        target = "target-postgres"
        if Globals.is_docker() == False:
            target += "-local"
        etc.helper_functions.call_network_docker("meltano", f"run tap-googleads {target}")
        await MeltanoManager.ConvertSQLToVectorStore(None)
        return web.Response(text="SQL -> Vector store conversion started.")
